# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 DrissionPage 的抖音数据抓取项目，专门用于获取抖音用户的基本信息、粉丝列表和点赞视频等数据。

## 运行命令

```bash
# 直接运行主脚本
python main.py
```

**注意**: 项目无 requirements.txt，需要手动安装依赖：
```bash
pip install DrissionPage jmespath
```

## 核心架构

### 主要组件
- `DouyinScraper`: 核心抓取类 (main.py:116)，封装了所有抖音数据获取功能
  - 使用 Chromium 浏览器进行数据抓取
  - 通过 DrissionPage 监听网络请求获取 API 数据
  - 内置滑块验证处理机制

### 数据流程
1. **网络监听**: 使用 `dp.listen.start()` 监听特定API端点
2. **页面访问**: 通过 `dp.get()` 访问抖音页面触发网络请求
3. **数据提取**: 从拦截的响应包中提取JSON数据
4. **分页处理**: 使用时间游标或普通游标进行翻页

### 关键技术
- **DrissionPage**: 浏览器自动化和网络监听
- **jmespath**: JSON 数据提取和解析
- **时间游标分页**: 使用 min_time/max_time 进行数据分页

## 关键功能模块

### 1. 用户信息获取
- `fetch_sec_uid(douyin_id)`: 通过抖音ID获取用户的sec_uid
- `fetch_user_profile(sec_uid)`: 获取用户基本信息

### 2. 粉丝数据抓取
- `fetch_followers(sec_uid, max_items=5000)`: 抓取用户粉丝列表
  - 支持时间游标分页 (min_time/max_time)
  - 内置去重机制和防风控间隔设置
  - 游标卡住时自动 -1 纠偏

### 3. 点赞数据抓取  
- `fetch_favorites(sec_uid, max_items=200)`: 获取用户点赞的视频列表
- `fetch_video_info(video_id)`: 获取单个视频详细信息

### 4. 数据导出
- `json_to_csv()`: 将JSON数据自动转换为CSV格式
- 支持嵌套字段提取和字段重命名

## 运行环境与配置

### 环境要求
- **Python 版本**: 3.7+ (使用了 `from __future__ import annotations`)
- **操作系统**: Windows
- **依赖库**: DrissionPage, jmespath

### 配置参数 (main.py:120-123)
```python
JS_TIMEOUT = 3                 # JS执行超时时间（秒）- ⚠️ 可能偏短，建议调至10
JS_RETRY = 2                   # 自动重试次数 - ⚠️ 建议增加到3
SLEEP_BETWEEN_TRIES = 0.8      # 重试间隔（秒）
SLEEP_BETWEEN_PAGES = 1.5      # 翻页间隔（秒）
```

**⚠️ 性能优化建议**：
- `JS_TIMEOUT` 建议增加到10秒，减少误判超时
- `JS_RETRY` 建议增加到3次，提高成功率
- 当前配置可能导致不必要的滑块验证触发

### 主函数配置 (main.py:668-677)
```python
Max_follower_count = 30        # 最大粉丝抓取数量
Max_favorite_count = 30        # 最大点赞视频数量
is_export_*_json = True        # 是否导出JSON
is_export_*_csv = True         # 是否导出CSV
```

## 风控处理机制

### 核心策略
- **统一JS拉取**: 使用 `_fetch_json_via_js()` 统一处理所有网络请求
- **自动重试**: JS超时或空返回时自动重试
- **滑块检测**: 失败后提示用户手动完成验证
- **用户交互**: 通过 `_wait_user_to_solve()` 暂停等待用户操作

### 处理流程
1. 尝试 `JS_RETRY` 次自动获取数据
2. 失败后提示用户过滑块验证
3. 验证完成后再次尝试 `JS_RETRY` 次
4. 仍失败则抛出异常

## 数据结构

### 用户信息字段
- nickname, uid, unique_id, followers, following, signature, aweme_count, favoriting_count

### 粉丝信息字段 (已重命名)
- 用户UID, 用户sec_uid, 用户抖音号, 用户昵称, 用户签名, 用户头像, 粉丝数, 关注数, 作品数, 获赞数等

### 视频信息字段 (已重命名)  
- 视频id, 视频描述, 创建时间, 视频作者信息, 点赞数, 评论数, 分享数, 音乐信息等

## 日志系统

- 使用 `setup_logger()` 初始化日志系统
- 同时输出到控制台和文件
- 日志文件命名格式: `douyin_scraper_YYYYMMDD_HHMMSS.log`
- 支持不同级别的日志记录 (DEBUG, INFO, WARNING, ERROR)

## 开发注意事项

### 项目特点
1. **单文件架构**: 所有功能集中在 `main.py` 中 (771行)
2. **无配置文件**: 需要直接修改代码中的配置参数
3. **防风控设计**: 多层防风控机制，包括重试、间隔、用户验证
4. **字段重命名**: 数据导出时自动进行中文字段重命名

### 代码结构
- main.py:14-44: 日志系统设置
- main.py:47-114: JSON转CSV工具函数  
- main.py:116-657: DouyinScraper核心类
- main.py:659-770: 主函数和配置

### 重要方法位置
- `_fetch_json_via_js()`: main.py:170 - 统一网络请求处理
- `_wait_user_to_solve()`: main.py:158 - 滑块验证处理
- `fetch_followers()`: main.py:255 - 粉丝列表抓取
- `fetch_favorites()`: main.py:527 - 点赞列表抓取
- **⚠️ `fetch_video_info()`: main.py:412 - 视频信息获取（存在性能瓶颈）**

### 已知性能问题
1. **视频信息获取慢**：单个视频处理时间6-18秒，主要原因：
   - 每个视频都重新设置监听器（main.py:417-418）
   - 使用低效的`listen.steps()`API（main.py:430）
   - 缺乏JSON解析异常处理（main.py:140-147）

2. **错误处理不当**：将JSON解析错误误判为滑块验证（main.py:446-448）

3. **串行处理架构**：无并发优化，总处理时间累积严重（main.py:618+）

## DrissionPage 4.x 最佳实践与性能优化

### 核心性能原则

1. **监听器复用**: 避免频繁的 `listen.clear() + listen.start()` 操作
2. **使用推荐API**: 优先使用 `listen.wait()` 而非 `listen.steps()`
3. **利用内置重试**: 避免手动实现DrissionPage已内置的功能
4. **加载模式优化**: 根据需求选择合适的页面加载模式

### API 使用指南

#### 网络监听最佳实践

```python
# ❌ 低效模式（当前项目存在的问题）
for video_id in video_list:
    self.dp.listen.clear()                    # 每次都清除监听
    self.dp.listen.start("api/target")        # 重新设置监听
    self.dp.get(f"https://example.com/{video_id}")
    pkt = next(self.dp.listen.steps(count=1)) # 使用低效API

# ✅ 高效模式（推荐）
self.dp.listen.start("api/target")  # 一次性设置监听
for video_id in video_list:
    self.dp.get(f"https://example.com/{video_id}")
    packet = self.dp.listen.wait(timeout=10, raise_err=True)  # 使用推荐API
```

#### 错误处理优化

```python
# ✅ 推荐的错误处理模式
try:
    packet = self.dp.listen.wait(timeout=10, raise_err=True)
    if packet and packet.response.body:
        data = json.loads(packet.response.body)
        return data
except TimeoutError:
    self.logger.warning("请求超时")
    return None
except json.JSONDecodeError as e:
    self.logger.warning(f"JSON解析失败: {e}")
    return None
except Exception as e:
    self.logger.error(f"未知错误: {e}")
    return None
```

#### 加载模式配置

```python
# 初始化时设置加载模式
def __init__(self):
    self.dp = Chromium().latest_tab
    # 根据需求选择加载模式
    self.dp.set.load_mode.none()    # 无加载模式：发送请求后立即返回（最快）
    # self.dp.set.load_mode.eager()  # 急切模式：DOM构建完成即返回
    # self.dp.set.load_mode.normal() # 正常模式：完整页面加载（默认）
```

### 性能优化策略

#### 1. 监听器生命周期管理
- **原则**: 一次设置，多次使用
- **实现**: 在批处理开始前设置监听，结束后清理
- **效果**: 节省1-2秒/视频的监听器重设时间

#### 2. API选择优化
- **使用 `listen.wait()`**: 内置超时和异常处理
- **避免 `listen.steps()`**: 需要手动迭代和错误处理
- **设置合理超时**: 根据网络环境调整timeout参数

#### 3. 错误分类处理
```python
def _classify_error(self, error_msg: str) -> str:
    """错误分类，避免将技术错误误判为滑块验证"""
    if "Expecting value" in str(error_msg):
        return "json_parse_error"    # JSON解析错误
    elif "timeout" in str(error_msg).lower():
        return "timeout_error"       # 超时错误
    elif "captcha" in str(error_msg).lower():
        return "captcha_required"    # 滑块验证
    else:
        return "unknown_error"       # 未知错误
```

#### 4. 配置参数调优
```python
# 推荐的配置参数（基于性能分析）
JS_TIMEOUT = 10              # 增加超时时间，减少误判
JS_RETRY = 3                 # 适当增加重试次数
SLEEP_BETWEEN_TRIES = 1.0    # 重试间隔
SLEEP_BETWEEN_PAGES = 1.5    # 保持防风控间隔
```

### 常见性能陷阱

1. **过度监听重设**: 每个请求都重新设置监听器
2. **使用过时API**: 使用低效的旧版本API
3. **手动重复实现**: 重新实现框架已有的功能
4. **错误分类不当**: 将技术错误误判为人工验证需求

### 性能监控建议

```python
# 添加性能监控
import time

def fetch_with_timing(self, video_id: str):
    start_time = time.time()
    try:
        result = self.fetch_video_info(video_id)
        elapsed = time.time() - start_time
        self.logger.info(f"视频 {video_id} 处理耗时: {elapsed:.2f}s")
        return result
    except Exception as e:
        elapsed = time.time() - start_time
        self.logger.error(f"视频 {video_id} 处理失败，耗时: {elapsed:.2f}s, 错误: {e}")
        raise
```

### 故障排查检查清单

当出现性能问题时，检查以下项目：
- [ ] 是否频繁调用 `listen.clear()` 和 `listen.start()`
- [ ] 是否使用了推荐的 `listen.wait()` API
- [ ] 是否正确设置了超时时间
- [ ] 是否利用了DrissionPage内置的重试机制
- [ ] 是否正确分类了不同类型的错误
- [ ] 加载模式是否根据需求进行了优化

### 版本兼容性说明

本项目当前使用DrissionPage 4.x版本，相比旧版本有以下主要变化：
- API命名和调用方式有所改变
- 性能大幅提升（异步模式提升320%，内存占用降低62%）
- 内置更强大的错误处理和重试机制
- 支持多标签页并发操作

迁移到新版本时需要注意API的变化，详见项目文档中的版本迁移指南。

## 紧急优化建议

### 立即可实施的修复（基于2025-07-28性能分析）

1. **修复JSON解析错误处理**：
```python
# 当前有问题的代码 (main.py:140-147)
def _to_json(body) -> dict:
    return json.loads(body)  # 缺乏异常处理

# 建议修复为：
def _to_json(body) -> dict:
    if not body:
        return {}
    try:
        return json.loads(body)
    except json.JSONDecodeError as e:
        logger.warning(f"JSON解析失败: {e}")
        return {}
```

2. **改进视频信息获取性能**：
```python
# 当前低效模式 (main.py:417-418, 430)
self.dp.listen.clear()  # 每个视频都重设
self.dp.listen.start(...)
pkt = next(self.dp.listen.steps(count=1))  # 低效API

# 推荐优化为：
# 在批处理开始时设置一次监听
self.dp.listen.start(...)
# 使用高效API
packet = self.dp.listen.wait(timeout=10, raise_err=True)
```

3. **优化配置参数**：
```python
JS_TIMEOUT = 10              # 从3秒增加到10秒
JS_RETRY = 3                 # 从2次增加到3次
```

### 性能提升预期
- 单视频处理时间：从6-18秒降至2-5秒
- 总体处理效率提升：60-70%
- 减少不必要的滑块验证触发

### 相关文档
- 详细分析：`docs/DrissionPage性能问题分析报告.md`
- 迁移指南：`docs/DrissionPage版本迁移指南.md`