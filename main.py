from __future__ import annotations

from DrissionPage import Chromium, ChromiumOptions
import jmespath
import json
from urllib.parse import urlsplit, parse_qsl, urlencode, urlunsplit
import time
import csv
import logging
import re
from datetime import datetime
from typing import List, Dict, Set, Union
# 导入工具函数
from utils import json_to_csv, get_nested_value, load_config, get_data_file_path
from logger import setup_logger
from douyin import DouyinScraper

def main():
    """主函数：演示抓取粉丝列表与喜欢列表。"""
    # 初始化日志系统
    logger = setup_logger("Main", logging.INFO)
    logger.info("=" * 60)
    logger.info("抖音数据抓取程序启动")
    logger.info("=" * 60)

    # 加载配置
    config = load_config()
    limits_config = config.get('limits', {})
    export_config = config.get('export', {})
    douyin_id_config = config.get('douyin_id', {})

    # 从配置文件读取参数
    Max_follower_count = limits_config.get('max_follower_count', 30)
    Max_favorite_count = limits_config.get('max_favorite_count', 30)

    # 导出选项
    is_export_follower_json = export_config.get('follower_json', True)
    is_export_follower_csv = export_config.get('follower_csv', True)
    is_export_favorite_json = export_config.get('favorite_json', True)
    is_export_favorite_csv = export_config.get('favorite_csv', True)

    #要爬取的抖音id
    douyin_id = douyin_id_config.get('douyin_id','')

    logger.info(f"配置参数 - 最大粉丝数: {Max_follower_count}, 最大喜欢数: {Max_favorite_count}")
    logger.info(f"导出选项 - JSON: {is_export_favorite_json}, CSV: {is_export_favorite_csv}")

    scraper = DouyinScraper()
    try:
        
        logger.info(f"开始处理用户 - 抖音ID: {douyin_id}")

        sec_uid = scraper.fetch_sec_uid(douyin_id)
        print(f"sec_uid: {sec_uid}\n{'='*60}")

        logger.info("开始获取用户基本信息")
        profile = scraper.fetch_user_profile(sec_uid)
        for k, v in profile.items():
            print(f"{k}: {v}")
        print('=' * 60)
        logger.info("用户基本信息获取完成")

        # # 粉丝列表（时间游标分页）
        # followers = scraper.fetch_followers(
        #     sec_uid=sec_uid,
        #     max_items=Max_follower_count,     # 按需调整
        #     page_count=20
        # )
        # print(f"\n粉丝抓取完成：{len(followers)} 条。")
        # if is_export_follower_json:
        #     # 保存粉丝详情到 JSON 文件
        #     json_filename = get_data_file_path(f"followers_details_{int(time.time())}.json")
        #     with open(json_filename, 'w', encoding='utf-8') as f:
        #         json.dump(followers, f, ensure_ascii=False, indent=2)
        #     print(f"粉丝详情已保存到 JSON 文件: {json_filename} (共{len(followers)}条记录)")

        # if is_export_follower_csv:
        #     # 导出粉丝详情到 CSV 文件
        #     csv_filename = get_data_file_path(f"followers_details_{int(time.time())}.csv")
        #     json_to_csv(followers, csv_filename)




        # 喜欢列表（可选）
        logger.info(f"开始获取喜欢列表 - 最大数量: {Max_favorite_count}")
        start_time = time.time()

        favorites = scraper.fetch_favorites(
            sec_uid=sec_uid,
            max_items=Max_favorite_count
        )

        elapsed_time = time.time() - start_time
        logger.info(f"喜欢列表获取完成 - 数量: {len(favorites)}, 总耗时: {elapsed_time:.2f}s")
        print(f"喜欢抓取完成：{len(favorites)} 条。")

        if is_export_favorite_json:
            logger.info("开始保存喜欢列表到 JSON 文件")
            # 保存喜欢列表到 JSON 文件
            favorites_json_filename = get_data_file_path(f"favorites_details_{int(time.time())}.json")
            with open(favorites_json_filename, 'w', encoding='utf-8') as f:
                json.dump(favorites, f, ensure_ascii=False, indent=2)
            logger.info(f"JSON 文件保存完成: {favorites_json_filename}")
            print(f"喜欢详情已保存到 JSON 文件: {favorites_json_filename} (共{len(favorites)}条记录)")
        if is_export_favorite_csv:
            # 导出喜欢列表到 CSV 文件
            favorites_csv_filename = get_data_file_path(f"favorites_details_{int(time.time())}.csv")
            json_to_csv(favorites, favorites_csv_filename)

        # 示例：获取单个视频详情
        # video_id = "7531226211713289530"  # 示例视频ID
        # try:
        #     video_info = scraper.fetch_video_info(video_id)
        #     print(f"视频详情获取完成：{video_info.get('视频描述', '')}")
        #
        #     # 保存视频详情到文件
        #     video_json_filename = f"video_detail_{video_id}_{int(time.time())}.json"
        #     with open(video_json_filename, 'w', encoding='utf-8') as f:
        #         json.dump(video_info, f, ensure_ascii=False, indent=2)
        #     print(f"视频详情已保存到: {video_json_filename}")
        # except Exception as e:
        #     print(f"获取视频详情失败: {e}")

    except Exception as e:
        logger.error(f"程序执行出现异常: {e}", exc_info=True)
        raise
    finally:
        # scraper.close()
        logger.info("=" * 60)
        logger.info("抖音数据抓取程序结束")
        logger.info("=" * 60)
        print(f"任务完成")


if __name__ == "__main__":
    main()
